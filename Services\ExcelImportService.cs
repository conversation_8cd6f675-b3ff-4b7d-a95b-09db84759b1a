
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using ClosedXML.Excel;
using Microsoft.EntityFrameworkCore;
using DriverManagementSystem.Models;
using DriverManagementSystem.Extensions;
using DriverManagementSystem.Data;

namespace DriverManagementSystem.Services
{
    /// <summary>
    /// خدمة استيراد البيانات من ملفات Excel (ClosedXML)
    /// </summary>
    public class ExcelImportService
    {
        private const int MAX_ITEMS = 50; // زيادة الحد الأقصى لاستيعاب جميع الصفوف
        private readonly IDataService _dataService;

        public ExcelImportService()
        {
            _dataService = new DataService();
        }

        /// <summary>
        /// استيراد بيانات الزيارة من ملف Excel مع التحقق من الصحة
        /// </summary>
        public async Task<FieldVisitImportResult> ImportFieldVisitFromExcel(string filePath, string indexValue)
        {
            var result = new FieldVisitImportResult();
            try
            {
                if (!File.Exists(filePath))
                {
                    result.ErrorMessage = $"الملف غير موجود: {filePath}";
                    return result;
                }

                using var wb = new XLWorkbook(filePath);

                // طباعة أسماء جميع الشيتات للتشخيص
                System.Diagnostics.Debug.WriteLine("🔍 أسماء الشيتات في الملف:");
                foreach (var sheet in wb.Worksheets)
                {
                    System.Diagnostics.Debug.WriteLine($"   - {sheet.Name}");
                }

                var mainSheet = wb.Worksheets.SingleOrDefault(ws => ws.Name.Trim().Equals("نموذج استمارة الزيارة الميدانية", StringComparison.OrdinalIgnoreCase));
                var projectSheet = wb.Worksheets.SingleOrDefault(ws => ws.Name.Contains("project_repeat", StringComparison.OrdinalIgnoreCase));
                var dayPlanSheet = wb.Worksheets.SingleOrDefault(ws => ws.Name.Contains("day_plan_repeat", StringComparison.OrdinalIgnoreCase));

                System.Diagnostics.Debug.WriteLine($"🔍 الشيت الرئيسي: {(mainSheet != null ? "موجود" : "مفقود")}");
                System.Diagnostics.Debug.WriteLine($"🔍 شيت المشاريع: {(projectSheet != null ? "موجود" : "مفقود")}");
                System.Diagnostics.Debug.WriteLine($"🔍 شيت خط السير: {(dayPlanSheet != null ? "موجود" : "مفقود")}");

                if (mainSheet == null || projectSheet == null || dayPlanSheet == null)
                {
                    result.ErrorMessage = "الملف يفتقد إحدى الشيتات المطلوبة.";
                    return result;
                }

                // ===== شيت الزيارة =====
                var mainTable = mainSheet.RangeUsed().AsTable();
                var visitRow   = mainTable.DataRange.Rows()
                                   .FirstOrDefault(r => r.Field("_index").GetString().Trim() == indexValue);

                if (visitRow == null)
                {
                    result.ErrorMessage = $"لم يتم العثور على الرقم المرجعي {indexValue}";
                    return result;
                }

                // معالجة نص القائمين بالزيارة (أكواد أو أسماء)
                var visitorsText = visitRow.Field("visitors").GetString().Trim();
                var visitorsNames = await ProcessVisitorCodesAsync(visitorsText);

                System.Diagnostics.Debug.WriteLine($"🔍 معالجة بيانات الزيارة من Excel:");
                System.Diagnostics.Debug.WriteLine($"   - نص القائمين بالزيارة الأصلي: '{visitorsText}'");
                System.Diagnostics.Debug.WriteLine($"   - أسماء القائمين بالزيارة المعالجة: '{visitorsNames}'");

                result.VisitData = new VisitImportData
                {
                    VisitFormNumber = visitRow.Field("visit_form_number").GetString().Trim(),
                    FieldDaysCount  = visitRow.Field("field_days_count").GetValue<int>(),
                    StartDate       = visitRow.Field("start_date").GetDateTime(),
                    EndDate         = visitRow.Field("end_date").GetDateTime(),
                    Sector          = visitRow.Field("sector").GetString().Trim(),
                    Visitors        = visitorsNames, // الأسماء المعالجة
                    VisitorCodes    = visitorsText, // النص الأصلي للمرجع
                    TripPurpose     = visitRow.Field("trip_purpose").GetString().Trim(),
                    SecurityRoute   = visitRow.Field("security_route").GetString().Trim(),
                    VisitNotes      = visitRow.Field("visit_notes").GetString().Trim(),

                    // الحقول الجديدة (اختيارية)
                    ApprovalBy      = TryGetFieldValue(visitRow, "approval_by"),
                    SubmissionTime  = TryGetFieldDateTime(visitRow, "_submission_time"),
                    OdkVisitNumber  = TryGetFieldValue(visitRow, "odk_visit_number")
                };

                // تشخيص إضافي للحقول الجديدة
                System.Diagnostics.Debug.WriteLine($"🔍 الحقول الجديدة من Excel:");
                System.Diagnostics.Debug.WriteLine($"   - approval_by: '{result.VisitData.ApprovalBy}'");
                System.Diagnostics.Debug.WriteLine($"   - _submission_time: '{result.VisitData.SubmissionTime}'");
                System.Diagnostics.Debug.WriteLine($"   - odk_visit_number: '{result.VisitData.OdkVisitNumber}'");
                System.Diagnostics.Debug.WriteLine($"   - visit_form_number: '{result.VisitData.VisitFormNumber}'");

                // ===== شيت المشاريع =====
                var projTable = projectSheet.RangeUsed().AsTable();
                foreach (var row in projTable.DataRange.Rows()
                                             .Where(r => r.Field("_parent_index").GetString().Trim() == indexValue)
                                             .Take(MAX_ITEMS))
                {
                    var projectCode = row.Field("project_code").GetString().Trim();
                    var projectName = row.Field("project_name").GetString().Trim();
                    var projectDays = row.Field("project_days").GetValue<int>();

                    result.Projects.Add(new ProjectImportData
                    {
                        ProjectCode = projectCode,
                        ProjectNumber = projectCode, // استخدام الكود كرقم المشروع
                        ProjectName = projectName,
                        ProjectDays = projectDays,
                        Notes = string.Empty
                    });

                    System.Diagnostics.Debug.WriteLine($"🔍 تم إضافة مشروع: {projectCode} - {projectName} ({projectDays} أيام)");
                }

                // ===== شيت خط السير =====
                var dayTable = dayPlanSheet.RangeUsed().AsTable();
                System.Diagnostics.Debug.WriteLine($"🔍 شيت خط السير: {dayPlanSheet.Name}");
                System.Diagnostics.Debug.WriteLine($"🔍 عدد الصفوف في شيت خط السير: {dayTable.DataRange.RowCount()}");

                var dayRows = dayTable.DataRange.Rows()
                                      .Where(r => r.Field("_parent_index").GetString().Trim() == indexValue)
                                      .OrderBy(r => r.Field("day_num").GetValue<int>())
                                      .Take(MAX_ITEMS)
                                      .ToList();

                System.Diagnostics.Debug.WriteLine($"🔍 عدد صفوف خط السير للرقم المرجعي {indexValue}: {dayRows.Count}");

                foreach (var row in dayRows)
                {
                    try
                    {
                        var dayNum = row.Field("day_num").GetValue<int>();
                        var plan = row.Field("day_plan").GetString();
                        System.Diagnostics.Debug.WriteLine($"🔍 اليوم {dayNum}: {plan}");

                        if (!string.IsNullOrWhiteSpace(plan))
                        {
                            result.Itinerary.Add(new ItineraryDayImportData
                            {
                                DayNumber = dayNum,
                                Plan = plan.Trim()
                            });
                            System.Diagnostics.Debug.WriteLine($"✅ تم إضافة اليوم {dayNum}");
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"⚠️ خط السير فارغ لليوم {dayNum}");
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"❌ خطأ في قراءة صف خط السير: {ex.Message}");
                    }
                }

                System.Diagnostics.Debug.WriteLine($"🎯 إجمالي أيام خط السير المستوردة: {result.Itinerary.Count}");

                // التحقق من صحة البيانات المستوردة
                var validationService = new ExcelValidationService(_dataService);
                var validationResult = await validationService.ValidateImportDataAsync(result);

                result.ValidationResult = validationResult;
                result.Success = validationResult.IsValid;

                if (!validationResult.IsValid)
                {
                    result.ErrorMessage = $"البيانات المستوردة تحتوي على أخطاء:\n{string.Join("\n", validationResult.Errors)}";
                    if (validationResult.HasWarnings)
                    {
                        result.ErrorMessage += $"\n\nتحذيرات:\n{string.Join("\n", validationResult.Warnings)}";
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"استثناء: {ex.Message}";
                return result;
            }
        }

        /// <summary>
        /// معالجة نص القائمين بالزيارة - إذا كان كود يبحث عن الاسم، وإذا كان اسم يبقى كما هو
        /// </summary>
        private async Task<string> ProcessVisitorCodesAsync(string visitorsText)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(visitorsText))
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ لا يوجد نص للقائمين بالزيارة");
                    return string.Empty;
                }

                // تقسيم النص (مفصول بمسافات أو فواصل)
                var items = visitorsText.Split(new[] { ' ', '\t', ',' }, StringSplitOptions.RemoveEmptyEntries)
                                       .Select(c => c.Trim())
                                       .Where(c => !string.IsNullOrEmpty(c))
                                       .ToList();

                System.Diagnostics.Debug.WriteLine($"🔍 عناصر القائمين بالزيارة: {string.Join(", ", items)}");

                if (items.Count == 0)
                {
                    return string.Empty;
                }

                // جلب جميع الموظفين للمقارنة
                using var context = new ApplicationDbContext();
                var officers = await context.Officers
                                           .Where(o => o.IsActive)
                                           .Select(o => new { o.Code, o.Name })
                                           .ToListAsync();

                // معالجة كل عنصر
                var processedNames = new List<string>();

                foreach (var item in items)
                {
                    // أولاً: البحث إذا كان كود موظف
                    var officerByCode = officers.FirstOrDefault(o => o.Code == item);
                    if (officerByCode != null)
                    {
                        processedNames.Add(officerByCode.Name);
                        System.Diagnostics.Debug.WriteLine($"✅ تم العثور على الكود {item} -> {officerByCode.Name}");
                        continue;
                    }

                    // ثانياً: البحث إذا كان اسم موظف موجود
                    var officerByName = officers.FirstOrDefault(o => o.Name.Contains(item) || item.Contains(o.Name));
                    if (officerByName != null)
                    {
                        processedNames.Add(officerByName.Name);
                        System.Diagnostics.Debug.WriteLine($"✅ تم العثور على الاسم {item} -> {officerByName.Name}");
                        continue;
                    }

                    // ثالثاً: إذا لم يوجد في قاعدة البيانات، استخدم النص كما هو
                    processedNames.Add(item);
                    System.Diagnostics.Debug.WriteLine($"📝 استخدام النص كما هو: {item}");
                }

                var result = string.Join(" - ", processedNames);
                System.Diagnostics.Debug.WriteLine($"🎯 النتيجة النهائية: {result}");
                return result;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في معالجة القائمين بالزيارة: {ex.Message}");
                // إرجاع النص الأصلي في حالة الخطأ
                return visitorsText;
            }
        }

        /// <summary>
        /// معالجة نص القائمين بالزيارة وتحويلها إلى FieldVisitor entities
        /// </summary>
        private async Task<List<FieldVisitor>> ProcessVisitorCodesToEntitiesAsync(string visitorsText)
        {
            var visitors = new List<FieldVisitor>();

            try
            {
                if (string.IsNullOrWhiteSpace(visitorsText))
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ لا يوجد نص للقائمين بالزيارة");
                    return visitors;
                }

                // تقسيم النص (مفصول بمسافات أو فواصل)
                var items = visitorsText.Split(new[] { ' ', '\t', ',' }, StringSplitOptions.RemoveEmptyEntries)
                                       .Select(c => c.Trim())
                                       .Where(c => !string.IsNullOrEmpty(c))
                                       .ToList();

                System.Diagnostics.Debug.WriteLine($"🔍 معالجة عناصر القائمين بالزيارة لإنشاء entities: {string.Join(", ", items)}");

                if (items.Count == 0)
                {
                    return visitors;
                }

                // جلب جميع الموظفين للمقارنة
                using var context = new ApplicationDbContext();
                var officers = await context.Officers
                                           .Where(o => o.IsActive)
                                           .ToListAsync();

                System.Diagnostics.Debug.WriteLine($"🔍 تم جلب {officers.Count} موظف من قاعدة البيانات");

                // إنشاء FieldVisitor entities
                foreach (var item in items)
                {
                    // أولاً: البحث إذا كان كود موظف
                    var officerByCode = officers.FirstOrDefault(o => o.Code == item);
                    if (officerByCode != null)
                    {
                        var visitor = new FieldVisitor
                        {
                            Name = officerByCode.Name,
                            OfficerId = officerByCode.Id,
                            OfficerName = officerByCode.Name,
                            OfficerRank = officerByCode.Rank ?? string.Empty,
                            OfficerCode = officerByCode.Code,
                            PhoneNumber = officerByCode.PhoneNumber ?? string.Empty
                        };

                        visitors.Add(visitor);
                        System.Diagnostics.Debug.WriteLine($"✅ تم إنشاء FieldVisitor للكود {item} -> {officerByCode.Name}");
                        continue;
                    }

                    // ثانياً: البحث إذا كان اسم موظف موجود
                    var officerByName = officers.FirstOrDefault(o => o.Name.Contains(item) || item.Contains(o.Name));
                    if (officerByName != null)
                    {
                        var visitor = new FieldVisitor
                        {
                            Name = officerByName.Name,
                            OfficerId = officerByName.Id,
                            OfficerName = officerByName.Name,
                            OfficerRank = officerByName.Rank ?? string.Empty,
                            OfficerCode = officerByName.Code,
                            PhoneNumber = officerByName.PhoneNumber ?? string.Empty
                        };

                        visitors.Add(visitor);
                        System.Diagnostics.Debug.WriteLine($"✅ تم إنشاء FieldVisitor للاسم {item} -> {officerByName.Name}");
                        continue;
                    }

                    // ثالثاً: إذا لم يوجد في قاعدة البيانات، إنشاء visitor بالنص كما هو
                    var genericVisitor = new FieldVisitor
                    {
                        Name = item,
                        OfficerId = 0,
                        OfficerName = item,
                        OfficerRank = string.Empty,
                        OfficerCode = string.Empty,
                        PhoneNumber = string.Empty
                    };

                    visitors.Add(genericVisitor);
                    System.Diagnostics.Debug.WriteLine($"📝 تم إنشاء FieldVisitor بالنص كما هو: {item}");
                }

                System.Diagnostics.Debug.WriteLine($"🎯 تم إنشاء {visitors.Count} FieldVisitor entities");
                return visitors;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في معالجة القائمين بالزيارة لإنشاء entities: {ex.Message}");
                return visitors;
            }
        }

        /// <summary>
        /// محاولة الحصول على قيمة نصية من حقل (مع معالجة الحقول المفقودة)
        /// </summary>
        private string TryGetFieldValue(IXLRangeRow row, string fieldName)
        {
            try
            {
                var cell = row.Field(fieldName);
                return cell?.GetString()?.Trim() ?? string.Empty;
            }
            catch
            {
                System.Diagnostics.Debug.WriteLine($"⚠️ الحقل '{fieldName}' غير موجود في ملف Excel");
                return string.Empty;
            }
        }

        /// <summary>
        /// محاولة الحصول على قيمة تاريخ من حقل (مع معالجة الحقول المفقودة)
        /// </summary>
        private DateTime? TryGetFieldDateTime(IXLRangeRow row, string fieldName)
        {
            try
            {
                var cell = row.Field(fieldName);
                return cell?.GetDateTime();
            }
            catch
            {
                System.Diagnostics.Debug.WriteLine($"⚠️ الحقل '{fieldName}' غير موجود في ملف Excel");
                return null;
            }
        }

        /// <summary>
        /// تحويل VisitImportData إلى FieldVisit entity وحفظها في قاعدة البيانات
        /// </summary>
        public async Task<(bool Success, string ErrorMessage, FieldVisit? SavedVisit)> ConvertAndSaveVisitAsync(FieldVisitImportResult importResult)
        {
            try
            {
                if (importResult?.VisitData == null)
                {
                    return (false, "بيانات الزيارة المستوردة مفقودة", null);
                }

                var visitData = importResult.VisitData;
                System.Diagnostics.Debug.WriteLine($"🔄 بدء تحويل وحفظ الزيارة: {visitData.VisitFormNumber}");

                // البحث عن القطاع
                Sector? sector = null;
                if (!string.IsNullOrWhiteSpace(visitData.Sector))
                {
                    sector = await _dataService.GetSectorByCodeAsync(visitData.Sector);
                    if (sector == null)
                    {
                        System.Diagnostics.Debug.WriteLine($"⚠️ القطاع '{visitData.Sector}' غير موجود، سيتم استخدام قطاع افتراضي");
                    }
                }

                // معالجة أكواد القائمين بالزيارة وتحويلها إلى entities
                var visitors = await ProcessVisitorCodesToEntitiesAsync(visitData.VisitorCodes);

                // تشخيص البيانات المحفوظة
                System.Diagnostics.Debug.WriteLine($"🔍 تشخيص القائمين بالزيارة المحفوظة:");
                foreach (var visitor in visitors)
                {
                    System.Diagnostics.Debug.WriteLine($"   - Name: '{visitor.Name}'");
                    System.Diagnostics.Debug.WriteLine($"   - OfficerName: '{visitor.OfficerName}'");
                    System.Diagnostics.Debug.WriteLine($"   - OfficerRank: '{visitor.OfficerRank}'");
                    System.Diagnostics.Debug.WriteLine($"   - OfficerCode: '{visitor.OfficerCode}'");
                }

                // إنشاء FieldVisit entity
                var fieldVisit = new FieldVisit
                {
                    VisitNumber = visitData.VisitFormNumber,
                    AddDate = DateTime.Now,
                    HijriDate = string.Empty, // يمكن إضافة تحويل التاريخ الهجري لاحقاً
                    DepartureDate = visitData.StartDate ?? DateTime.Now,
                    ReturnDate = visitData.EndDate ?? DateTime.Now.AddDays(visitData.FieldDaysCount),
                    DaysCount = visitData.FieldDaysCount,
                    MissionPurpose = visitData.TripPurpose,
                    SectorId = sector?.Id ?? 0,
                    SectorName = sector?.Name ?? visitData.Sector,
                    VisitorsCount = visitors.Count,
                    Visitors = visitors,
                    SecurityRoute = visitData.SecurityRoute,
                    VisitNotes = visitData.VisitNotes,
                    ProjectsCount = importResult.Projects.Count,
                    Projects = ConvertProjectsToEntities(importResult.Projects),
                    Itinerary = importResult.Itinerary.Select(i => i.ItineraryText).ToList(),
                    CreatedAt = DateTime.Now,
                    IsActive = true
                };

                System.Diagnostics.Debug.WriteLine($"📊 تم إنشاء FieldVisit entity:");
                System.Diagnostics.Debug.WriteLine($"   - رقم الزيارة: {fieldVisit.VisitNumber}");
                System.Diagnostics.Debug.WriteLine($"   - عدد الأيام: {fieldVisit.DaysCount}");
                System.Diagnostics.Debug.WriteLine($"   - عدد القائمين بالزيارة: {fieldVisit.VisitorsCount}");
                System.Diagnostics.Debug.WriteLine($"   - عدد المشاريع: {fieldVisit.ProjectsCount}");
                System.Diagnostics.Debug.WriteLine($"   - عدد أيام خط السير: {fieldVisit.Itinerary.Count}");

                // حفظ في قاعدة البيانات
                var (success, errors) = await _dataService.AddFieldVisitAsync(fieldVisit);

                if (success)
                {
                    System.Diagnostics.Debug.WriteLine($"✅ تم حفظ الزيارة بنجاح: {fieldVisit.VisitNumber}");
                    return (true, string.Empty, fieldVisit);
                }
                else
                {
                    var errorMessage = $"فشل في حفظ الزيارة: {string.Join(", ", errors)}";
                    System.Diagnostics.Debug.WriteLine($"❌ {errorMessage}");
                    return (false, errorMessage, null);
                }
            }
            catch (Exception ex)
            {
                var errorMessage = $"خطأ في تحويل وحفظ الزيارة: {ex.Message}";
                System.Diagnostics.Debug.WriteLine($"❌ {errorMessage}");
                return (false, errorMessage, null);
            }
        }

        /// <summary>
        /// تحويل ProjectImportData إلى FieldVisitProject entities
        /// </summary>
        private List<FieldVisitProject> ConvertProjectsToEntities(List<ProjectImportData> projects)
        {
            var result = new List<FieldVisitProject>();

            foreach (var project in projects)
            {
                var fieldVisitProject = new FieldVisitProject
                {
                    ProjectNumber = project.ProjectNumber,
                    ProjectName = project.ProjectName,
                    ProjectDays = project.ProjectDays,
                    Notes = project.Notes ?? string.Empty,
                    CreatedAt = DateTime.Now,
                    IsActive = true
                };

                result.Add(fieldVisitProject);
            }

            System.Diagnostics.Debug.WriteLine($"🔄 تم تحويل {result.Count} مشروع إلى entities");
            return result;
        }
    }

    #region DTOs
    public class FieldVisitImportResult
    {
        public bool Success { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
        public VisitImportData? VisitData { get; set; }
        public List<ProjectImportData> Projects { get; set; } = new();
        public List<ItineraryDayImportData> Itinerary { get; set; } = new();
        public ValidationResult? ValidationResult { get; set; }
    }

    public class VisitImportData
    {
        public string VisitFormNumber { get; set; } = string.Empty;
        public int FieldDaysCount { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string Sector { get; set; } = string.Empty;
        public string Visitors { get; set; } = string.Empty; // أسماء القائمين بالزيارة
        public string VisitorCodes { get; set; } = string.Empty; // أكواد القائمين بالزيارة الأصلية
        public string TripPurpose { get; set; } = string.Empty;
        public string SecurityRoute { get; set; } = string.Empty;
        public string VisitNotes { get; set; } = string.Empty;

        // الحقول الجديدة
        public string ApprovalBy { get; set; } = string.Empty; // الموافقة على السفر
        public DateTime? SubmissionTime { get; set; } // وقت وتاريخ الإرسال
        public string OdkVisitNumber { get; set; } = string.Empty; // رقم الزيارة ODK
    }

    public class ProjectImportData
    {
        public string ProjectCode { get; set; } = string.Empty;
        public string ProjectNumber { get; set; } = string.Empty;
        public string ProjectName { get; set; } = string.Empty;
        public int ProjectDays { get; set; }
        public string? Notes { get; set; } = string.Empty;
    }

    public class ItineraryDayImportData
    {
        public int DayNumber { get; set; }
        public string Plan { get; set; } = string.Empty;
        public string ItineraryText => Plan; // للتوافق مع FieldVisit.Itinerary
    }
    #endregion
}
